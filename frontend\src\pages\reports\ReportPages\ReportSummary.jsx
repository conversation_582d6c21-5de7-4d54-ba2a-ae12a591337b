import React from 'react';
import { reportSummaryData } from './data.js';

const ReportSummary = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {}
}) => {

  // A4 page height in pixels (approximately 1123px at 96 DPI)
  const A4_PAGE_HEIGHT = 1123;
  const HEADER_HEIGHT = 120; // Approximate height of header section
  const AVAILABLE_HEIGHT = A4_PAGE_HEIGHT - HEADER_HEIGHT - 40; // 40px for padding

  // Helper function to render header
  const renderHeader = () => (
    <div className="flex items-center justify-between gap-2 border-b-4 border-blue-900 mb-6">
      <h1 style={headerTextStyle}>
        {reportSummaryData.header.mainTitle}
      </h1>
      <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
        {reportSummaryData.header.headerSubtext}
      </p>
    </div>
  );

  // Helper function to render content items
  const renderContentItem = (item, index) => {
    if (typeof item === 'string') {
      return (
        <p key={index} className="leading-relaxed" style={contentTextStyle}>
          {item}
        </p>
      );
    }

    if (item.type === 'paragraph') {
      return (
        <p key={index} className="leading-relaxed" style={contentTextStyle}>
          <span className="font-semibold">{item.text.split(':')[0]}:</span> {item.text.split(':')[1]}
        </p>
      );
    }

    if (item.type === 'subsection') {
      return (
        <div key={index}>
          <p className="leading-relaxed font-semibold" style={{ ...subHeadingTextStyle, fontWeight: "bold", color: "black", marginTop: '-4px', marginBottom: '-4px' }}>
            {item.title}
          </p>
          <div>
            {item.items.map((subItem, subIndex) => (
              <p key={subIndex} className="leading-relaxed" style={contentTextStyle}>
                {subItem}
              </p>
            ))}
          </div>
        </div>
      );
    }

    return null;
  };

  // Helper function to render analysis sections
  const renderAnalysisSection = (section, index) => (
    <div key={index} className="mb-2">
      <h3
        style={{ ...headingTextStyle, color: 'black', marginBottom: "-7px" }}
      >
        {section.title}
      </h3>
      <div>
        {section.content.map((item, itemIndex) => renderContentItem(item, itemIndex))}
      </div>

      <div>
        <div
          style={{ ...contentTextStyle, fontSize: "20px", fontWeight: "bold", color: 'black', marginTop: '-5px', marginBottom: "-5px" }}
        >
          Recommendation
        </div>
        <div
          className="text-gray-700 leading-relaxed"
          style={contentTextStyle}
        >
          {section.recommendation}
        </div>
        {section.additionalRecommendation && (
          <p style={contentTextStyle}>
            {section.additionalRecommendation}
          </p>
        )}
      </div>
    </div>
  );

  // Function to create pages with content that fits A4 height
  const createPages = () => {
    const allContent = [
      {
        type: 'section',
        title: reportSummaryData.executiveSummary.title,
        content: reportSummaryData.executiveSummary.content,
        isExecutiveSummary: true
      },
      {
        type: 'section',
        title: reportSummaryData.analysisAndRecommendations.title,
        content: reportSummaryData.analysisAndRecommendations.sections,
        isAnalysisSection: true
      },
      {
        type: 'section',
        title: reportSummaryData.keyFocusAreas.title,
        content: reportSummaryData.keyFocusAreas.content,
        isKeyFocusAreas: true
      },
      {
        type: 'disclaimer',
        content: reportSummaryData.disclaimer
      }
    ];

    const newPages = [];
    let currentPage = [];
    let currentHeight = 0;

    allContent.forEach((section) => {
      // Estimate section height (this is approximate)
      let sectionHeight = 0;

      if (section.type === 'section') {
        sectionHeight += 60; // Title height

        if (section.isExecutiveSummary || section.isKeyFocusAreas) {
          // Simple text content
          const textLines = Math.ceil(section.content.length / 80); // Approximate chars per line
          sectionHeight += textLines * 24; // Line height
        } else if (section.isAnalysisSection) {
          // Analysis sections with subsections
          section.content.forEach(analysisSection => {
            sectionHeight += 40; // Section title
            sectionHeight += analysisSection.content.length * 30; // Content items
            sectionHeight += 60; // Recommendation section
          });
        }
      } else if (section.type === 'disclaimer') {
        sectionHeight += 100; // Disclaimer box
      }

      // Check if adding this section would exceed page height
      if (currentHeight + sectionHeight > AVAILABLE_HEIGHT && currentPage.length > 0) {
        // Start new page
        newPages.push([...currentPage]);
        currentPage = [section];
        currentHeight = sectionHeight;
      } else {
        // Add to current page
        currentPage.push(section);
        currentHeight += sectionHeight;
      }
    });

    // Add the last page if it has content
    if (currentPage.length > 0) {
      newPages.push(currentPage);
    }

    return newPages.length > 0 ? newPages : [allContent];
  };

  // Function to render a section
  const renderSection = (section, index) => {
    if (section.type === 'section') {
      if (section.isExecutiveSummary || section.isKeyFocusAreas) {
        return (
          <div key={index} className="bg-white mb-6">
            <div
              className="text-2xl font-semibold text-teal-600 mb-4"
              style={headingTextStyle}
            >
              {section.title}
            </div>
            <p
              className="text-lg leading-relaxed text-gray-700 mb-6"
              style={contentTextStyle}
            >
              {section.content}
            </p>
          </div>
        );
      } else if (section.isAnalysisSection) {
        return (
          <div key={index} className="bg-white mb-6">
            <div
              className="text-2xl font-semibold text-teal-600 mb-5"
              style={headingTextStyle}
            >
              {section.title}
            </div>
            <div>
              {section.content.map(renderAnalysisSection)}
            </div>
          </div>
        );
      }
    } else if (section.type === 'disclaimer') {
      return (
        <div key={index} className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>
          <p>
            {section.content}
          </p>
        </div>
      );
    }
    return null;
  };

  const pagesContent = createPages();

  return (
    <div>
      {pagesContent.map((pageContent, pageIndex) => (
        <div
          key={pageIndex}
          className="max-w-6xl mx-auto bg-white flex flex-col gap-6 p-10 mb-2"
          style={{
            minHeight: `${A4_PAGE_HEIGHT}px`,
            maxHeight: `${A4_PAGE_HEIGHT}px`,
            pageBreakAfter: pageIndex < pagesContent.length - 1 ? 'always' : 'auto',
            overflow: 'hidden'
          }}
        >
          {/* Header - repeated on each page */}
          {renderHeader()}

          {/* Page Content */}
          <div>
            {pageContent.map(renderSection)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ReportSummary;